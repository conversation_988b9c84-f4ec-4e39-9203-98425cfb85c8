"""Memory-optimized streaming Excel service for large datasets (300k+ rows)."""

import pandas as pd
import numpy as np
import xlsxwriter
import io
import logging
import psutil
import os
from datetime import datetime
from typing import Dict, List, Optional, Iterator, Union, Tuple
from google.cloud import storage
from dal.database_connector import DatabaseConnector
from dal.bigquery_connector import BigQueryConnector

logger = logging.getLogger(__name__)


class StreamingExcelService:
    """Memory-optimized Excel service that streams data directly to GCS."""
    
    def __init__(self, 
                 db_connector: DatabaseConnector = None, 
                 bq_connector: BigQueryConnector = None,
                 storage_client: storage.Client = None,
                 bucket_name: str = None):
        """Initialize streaming Excel service.
        
        Args:
            db_connector: PostgreSQL database connector
            bq_connector: BigQuery connector
            storage_client: GCS storage client
            bucket_name: GCS bucket name
        """
        self.db_connector = db_connector or DatabaseConnector()
        self.bq_connector = bq_connector or BigQueryConnector()
        self.storage_client = storage_client
        self.bucket_name = bucket_name
        
        # Memory optimization settings - conservative for full functionality
        self.chunk_size = 1000  # Process 1k rows at a time to maintain all columns
        self.max_memory_mb = 512  # Increased target for full functionality
        
        # Excel formatting styles
        self.header_format_config = {
            'bold': True,
            'font_color': 'white',
            'bg_color': '#1E3A5F',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'font_size': 10
        }
        
        self.data_format_config = {
            'font_size': 9,
            'align': 'left',
            'valign': 'vcenter',
            'border': 1,
            'border_color': '#CCCCCC'
        }
    
    def generate_excel_to_gcs(self, tenant_id: str, gcs_blob_name: str) -> str:
        """Generate Excel report directly to GCS with memory optimization.
        
        Args:
            tenant_id: Tenant identifier
            gcs_blob_name: GCS blob name for the output file
            
        Returns:
            GCS path of the generated file
        """
        start_time = datetime.now()
        process = psutil.Process(os.getpid())
        initial_memory_mb = process.memory_info().rss / 1024 / 1024
        
        logger.info(f"Starting memory-optimized Excel generation for tenant: {tenant_id}")
        logger.info(f"Initial memory usage: {initial_memory_mb:.1f} MB")
        
        try:
            # Create BytesIO buffer for Excel file
            excel_buffer = io.BytesIO()
            
            # Create workbook with aggressive memory optimization
            workbook = xlsxwriter.Workbook(excel_buffer, {
                'constant_memory': True,  # Critical for large files
                'tmpdir': '/tmp',
                'default_date_format': 'yyyy-mm-dd',
                'remove_timezone': True,
                'use_zip64': True  # Support very large files
            })
            
            # Create format objects
            header_format = workbook.add_format(self.header_format_config)
            data_format = workbook.add_format(self.data_format_config)
            
            # Generate sheets with streaming
            logger.info("Creating account sheet...")
            account_start = datetime.now()
            self._create_account_sheet_streaming(workbook, tenant_id, header_format, data_format)
            account_duration = (datetime.now() - account_start).total_seconds()
            logger.info(f"Account sheet completed in {account_duration:.1f} seconds")
            
            logger.info("Creating vessel sheet...")
            vessel_start = datetime.now()
            self._create_vessel_sheet_streaming(workbook, tenant_id, header_format, data_format)
            vessel_duration = (datetime.now() - vessel_start).total_seconds()
            logger.info(f"Vessel sheet completed in {vessel_duration:.1f} seconds")
            
            # Close workbook to finalize
            logger.info("Finalizing Excel file...")
            workbook.close()
            
            # Upload to GCS
            logger.info("Uploading to GCS...")
            upload_start = datetime.now()
            gcs_path = self._upload_buffer_to_gcs(excel_buffer, gcs_blob_name)
            upload_duration = (datetime.now() - upload_start).total_seconds()
            
            # Final memory check
            final_memory_mb = process.memory_info().rss / 1024 / 1024
            memory_delta_mb = final_memory_mb - initial_memory_mb
            
            total_duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"Excel report completed in {total_duration:.1f} seconds (account: {account_duration:.1f}s, vessel: {vessel_duration:.1f}s, upload: {upload_duration:.1f}s)")
            logger.info(f"Memory usage: {initial_memory_mb:.1f} MB → {final_memory_mb:.1f} MB (Δ{memory_delta_mb:+.1f} MB)")
            logger.info(f"Generated file: {gcs_path}")
            
            return gcs_path
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"Error in streaming Excel generation after {duration:.1f} seconds: {e}")
            raise
    
    def _create_account_sheet_streaming(self, workbook, tenant_id: str, header_format, data_format):
        """Create account sheet with streaming data processing."""
        logger.info("Creating account sheet with streaming")
        
        worksheet = workbook.add_worksheet("Account Level Report")
        
        # Get account data in chunks and write directly
        row = 0
        headers_written = False
        chunk_count = 0
        total_accounts_processed = 0
        
        for chunk_df in self._get_account_data_chunks(tenant_id):
            if chunk_df.empty:
                continue
            
            chunk_count += 1
            chunk_size = len(chunk_df)
            total_accounts_processed += chunk_size
            
            # Write headers only once
            if not headers_written:
                for col, header in enumerate(chunk_df.columns):
                    worksheet.write(row, col, header, header_format)
                row += 1
                headers_written = True
                logger.info(f"Account sheet headers written ({len(chunk_df.columns)} columns)")
            
            # Write data rows
            start_row = row
            for _, data_row in chunk_df.iterrows():
                for col, value in enumerate(data_row):
                    formatted_value = self._format_cell_value(value)
                    worksheet.write(row, col, formatted_value, data_format)
                row += 1
            
            logger.info(f"Account chunk {chunk_count} written: {chunk_size} accounts (rows {start_row}-{row-1}), total processed: {total_accounts_processed}")
            
            # Clear chunk from memory
            del chunk_df
        
        logger.info(f"Account sheet completed: {chunk_count} chunks, {total_accounts_processed} total accounts, {row-1} data rows")
        
        # Auto-adjust column widths
        self._auto_adjust_columns(worksheet, 500)  # Max 500 columns
    
    def _create_vessel_sheet_streaming(self, workbook, tenant_id: str, header_format, data_format):
        """Create vessel sheet with streaming data processing."""
        logger.info("Creating vessel sheet with streaming")
        
        worksheet = workbook.add_worksheet("Vessel Level Report")

        # Define vessel sheet columns with display names (excluding account_id and policy_id)
        vessel_columns_mapping = {
            'account_name': 'Account Name',
            'policy_number': 'UMR',
            'imo': 'IMO',
            'vessel_name': 'Vessel Name',
            'vessel_risk_score': 'Vessel Risk Score',
            'expected_loss': 'Expected Loss',
            'frequency': 'Frequency',
            'severity': 'Severity',
            'comparative_vessel_el': 'Comparative Vessel EL',
            'yearofbuild': 'Year Of Build',
            'total_inspections': 'Total Inspections',
            'total_defects': 'Total Defects',
            'total_detentions': 'Total Detentions',
            'avg_defects_per_inspection': 'Avg Defects Per Inspection',
            'avg_detentions_per_inspection': 'Avg Detentions Per Inspection'
        }

        # Get vessel data in chunks and write directly
        row = 0
        headers_written = False
        chunk_count = 0
        total_vessels_processed = 0
        
        for chunk_df in self._get_vessel_data_chunks(tenant_id):
            if chunk_df.empty:
                continue
            
            chunk_count += 1
            chunk_size = len(chunk_df)
            total_vessels_processed += chunk_size

            # Filter and rename columns for vessel sheet
            filtered_chunk = self._filter_and_rename_vessel_columns(chunk_df, vessel_columns_mapping)

            # Write headers only once
            if not headers_written:
                for col, header in enumerate(filtered_chunk.columns):
                    worksheet.write(row, col, header, header_format)
                row += 1
                headers_written = True
                logger.info(f"Vessel sheet headers written ({len(filtered_chunk.columns)} columns)")

            # Write data rows
            start_row = row
            for _, data_row in filtered_chunk.iterrows():
                for col, value in enumerate(data_row):
                    formatted_value = self._format_cell_value(value)
                    worksheet.write(row, col, formatted_value, data_format)
                row += 1
            
            logger.info(f"Vessel chunk {chunk_count} written: {chunk_size} vessels (rows {start_row}-{row-1}), total processed: {total_vessels_processed}")
            
            # Clear chunk from memory
            del chunk_df
            del filtered_chunk
        
        logger.info(f"Vessel sheet completed: {chunk_count} chunks, {total_vessels_processed} total vessels, {row-1} data rows")
        
        # Auto-adjust column widths
        self._auto_adjust_columns(worksheet, 500)  # Max 500 columns

    def _filter_and_rename_vessel_columns(self, vessel_data: pd.DataFrame, columns_mapping: dict) -> pd.DataFrame:
        """Filter and rename vessel columns for Excel output."""
        # Get available columns that exist in both the data and our mapping
        available_columns = []
        for col in columns_mapping.keys():
            if col in vessel_data.columns:
                available_columns.append(col)

        # Add risk driver columns (they follow a pattern)
        risk_driver_columns = []
        for col in vessel_data.columns:
            if col.startswith('Risk driver '):
                risk_driver_columns.append(col)
                available_columns.append(col)

        # Add feature columns (they follow a pattern)
        feature_columns = []
        for col in vessel_data.columns:
            if col.startswith('Influential factor '):
                feature_columns.append(col)
                available_columns.append(col)

        # Filter the dataframe to only include available columns
        filtered_data = vessel_data[available_columns].copy()

        # Rename columns using the mapping (only for base columns, not risk drivers/features)
        rename_dict = {}
        for col in filtered_data.columns:
            if col in columns_mapping:
                rename_dict[col] = columns_mapping[col]
            # Risk driver and feature columns keep their original names

        if rename_dict:
            filtered_data = filtered_data.rename(columns=rename_dict)

        return filtered_data

    def _get_account_data_chunks(self, tenant_id: str) -> Iterator[pd.DataFrame]:
        """Get account data with database-level aggregation for memory efficiency."""
        try:
            logger.info(f"Streaming account data for tenant: {tenant_id} with database aggregation")
            
            # Get account aggregation directly from database to avoid loading all vessel data
            # Simplified account aggregation query for PostgreSQL only (no BigQuery inspection data)
            account_aggregation_query = """
            SELECT 
                vpi.account_name as "Account Name",
                vpi.policy_number as "Policy Number",
                ROUND(AVG(COALESCE(vms.score, 0)), 0) as "Account Risk Score",
                ROUND(AVG(COALESCE(vms.freq_score, 0)), 0) as "Frequency",
                ROUND(AVG(COALESCE(vms.sev_score, 0)), 0) as "Severity",
                ROUND(SUM(COALESCE(vms.detail_score, 0)), 0) as "Total expected loss (in USD)",
                ROUND(SUM(COALESCE(
                    CASE 
                        WHEN vms.vessel_type_detail_score IS NULL OR vms.vessel_type_detail_score < 0 
                        THEN vms.detail_score 
                        ELSE vms.vessel_type_detail_score 
                    END, 0)), 0) as "Comparative Account EL (in USD)",
                COUNT(vpi.imo) as "No. of Vessels",
                0 as "Avg. year of build",
                0 as "Average no. of defects per inspection",
                0 as "Average no. of detentions per inspection"
            FROM policy_detail.vessel_policy_info vpi
            LEFT JOIN risk_scores.vessel_model_scores_10 vms ON vpi.imo = vms.imo 
                AND vms.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_model_scores_10)
            WHERE vpi.tenant_id = :tenant_id
            GROUP BY vpi.policy_id, vpi.account_name, vpi.policy_number
            ORDER BY vpi.policy_id
            """
            
            # Execute aggregation query directly in PostgreSQL database
            account_data = self.db_connector.execute_query(account_aggregation_query, {'tenant_id': tenant_id})
            
            if account_data.empty:
                logger.warning(f"No account data found for tenant: {tenant_id}")
                yield pd.DataFrame()
                return
            
            logger.info(f"Database aggregation completed: {len(account_data)} accounts ({len(account_data.columns)} columns)")
            
            # Add empty risk driver and feature columns to match original functionality exactly
            # Note: Account-level risk drivers/features require complex vessel-level aggregation
            # For now, we'll add empty columns with same structure as original (10 each)
            max_account_drivers = 10  # Same as original
            max_account_features = 10  # Same as original
            
            for i in range(1, max_account_drivers + 1):
                account_data[f'Risk driver {i}'] = '-'
                account_data[f'Risk driver {i} (in %)'] = '-'
                account_data[f'Risk driver sign {i}'] = '-'
            
            for i in range(1, max_account_features + 1):
                account_data[f'Influential factor {i}'] = '-'
                account_data[f'Influential factor {i} value'] = '-'
                account_data[f'Influential factor {i} percentage'] = '-'
                account_data[f'Influential factor {i} (Risk-driver)'] = '-'
                account_data[f'Influential factor {i} sign'] = '-'
            
            # Yield account data in chunks
            for i in range(0, len(account_data), self.chunk_size):
                chunk = account_data.iloc[i:i + self.chunk_size].copy()
                yield chunk
                
        except Exception as e:
            logger.error(f"Error streaming account data with database aggregation: {e}")
            # Fallback to simplified account data
            try:
                logger.info("Using fallback account query")
                simple_query = """
                SELECT
                    account_name as "Account Name",
                    policy_number as "Policy Number",
                    COUNT(imo) as "No. of Vessels",
                    0 as "Account Risk Score",
                    0 as "Frequency",
                    0 as "Severity",
                    0 as "Total expected loss (in USD)",
                    0 as "Comparative Account EL (in USD)",
                    0 as "Avg. year of build",
                    0 as "Average no. of defects per inspection",
                    0 as "Average no. of detentions per inspection"
                FROM policy_detail.vessel_policy_info
                WHERE tenant_id = :tenant_id
                GROUP BY policy_id, account_name, policy_number
                ORDER BY policy_id
                """
                fallback_data = self.db_connector.execute_query(simple_query, {'tenant_id': tenant_id})
                
                if not fallback_data.empty:
                    # Add empty risk driver and feature columns
                    for i in range(1, 11):
                        fallback_data[f'Risk driver {i}'] = '-'
                        fallback_data[f'Risk driver {i} (in %)'] = '-'
                        fallback_data[f'Risk driver sign {i}'] = '-'
                    
                    for i in range(1, 11):
                        fallback_data[f'Influential factor {i}'] = '-'
                        fallback_data[f'Influential factor {i} value'] = '-'
                        fallback_data[f'Influential factor {i} percentage'] = '-'
                        fallback_data[f'Influential factor {i} (Risk-driver)'] = '-'
                        fallback_data[f'Influential factor {i} sign'] = '-'
                    
                    logger.info(f"Fallback account data: {len(fallback_data)} accounts")
                    yield fallback_data
                else:
                    yield pd.DataFrame()
            except Exception as fallback_error:
                logger.error(f"Fallback account query also failed: {fallback_error}")
                yield pd.DataFrame()
    
    def _get_vessel_data_chunks(self, tenant_id: str) -> Iterator[pd.DataFrame]:
        """Get vessel data using original logic but in memory-efficient chunks."""
        try:
            logger.info(f"Collecting vessel data for tenant: {tenant_id} using original approach")
            
            # Get vessel policy information (same as original)
            vessel_policy_query = """
            SELECT
                imo,
                policy_id,
                policy_number,
                account_id,
                account_name
            FROM policy_detail.vessel_policy_info
            WHERE tenant_id = :tenant_id
            """
            vessel_policy_df = self.db_connector.execute_query(
                vessel_policy_query, 
                {'tenant_id': tenant_id}
            )
            
            if vessel_policy_df.empty:
                logger.warning(f"No vessel policy data found for tenant: {tenant_id}")
                yield pd.DataFrame()
                return
            
            # Get risk scores (same as original)
            logger.info("Getting risk scores...")
            risk_scores_query = """
            SELECT 
                imo,
                ROUND(score, 0) as vessel_risk_score,
                ROUND(detail_score, 0) as expected_loss,
                ROUND(freq_score, 0) as frequency,
                ROUND(sev_score, 0) as severity,
                ROUND(CASE 
                    WHEN vessel_type_detail_score IS NULL OR vessel_type_detail_score < 0 
                    THEN detail_score 
                    ELSE vessel_type_detail_score 
                END, 0) as comparative_vessel_el
            FROM risk_scores.vessel_model_scores_10
            WHERE created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_model_scores_10)
            """
            risk_scores_df = self.db_connector.execute_query(risk_scores_query)
            
            # Get vessel risk drivers (same as original)
            logger.info("Getting risk drivers...")
            risk_drivers_query = """
            SELECT 
                vf.imo,
                vf.riskdriver AS "driverName",
                round(sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END)),4) AS "positiveInfluencePercentage",
                round(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)),4) AS "negativeInfluencePercentage",
                round(abs(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) - sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))),4) AS "netInfluencePercentage",
                CASE 
                    WHEN sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) > sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))
                    THEN 'POSITIVE'
                    ELSE 'NEGATIVE'
                END AS "riskStatus"
            FROM risk_scores.vessel_feature_values_10 vf
            WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
            GROUP BY vf.imo, vf.riskdriver
            ORDER BY vf.imo, "netInfluencePercentage" DESC
            """
            risk_drivers_df = self.db_connector.execute_query(risk_drivers_query)
            
            # Get vessel features (same as original)
            logger.info("Getting vessel features...")
            vessel_imos = vessel_policy_df['imo'].dropna().unique()
            imo_list = [int(imo) for imo in vessel_imos if pd.notna(imo)]
            
            if imo_list:
                imo_values = ','.join([str(imo) for imo in imo_list])
                features_query = f"""
                SELECT 
                    ranked_vf.imo,
                    ranked_vf.feature as "key",
                    fd.field_name as "fieldName",
                    fd.description as "description",
                    fd.unit as "featureValueUnit",
                    abs(round(ranked_vf.shap_impact_percentage,4))::real as "riskValue",
                    ranked_vf.riskdriver as "riskDriver",
                    abs(round(ranked_vf.shap_impact_percentage,4))::real as "featurePercentage",
                    ranked_vf.shap_impact_sign as "riskStatus",
                    abs(round(ranked_vf.feature_value,4))::real as "featureValue"
                FROM (
                    SELECT vf.imo, vf.feature, vf.shap_impact_percentage, vf.riskdriver, vf.shap_impact_sign, vf.feature_value, vf.created_on,
                           ROW_NUMBER() OVER (PARTITION BY vf.imo ORDER BY abs(vf.shap_impact_percentage) DESC) as rn
                    FROM risk_scores.vessel_feature_values_10 vf
                    WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
                    AND vf.imo IN ({imo_values})
                ) ranked_vf
                LEFT JOIN risk_scores.feature_dictionary_10 fd ON ranked_vf.feature = fd.name AND ranked_vf.created_on = fd.created_on
                WHERE ranked_vf.rn <= 100
                ORDER BY ranked_vf.imo, ranked_vf.shap_impact_percentage DESC
                """
                features_df = self.db_connector.execute_query(features_query)
            else:
                features_df = pd.DataFrame()
            
            # Get inspection data from BigQuery (same as original)
            logger.info("Getting inspection data...")
            project_id = self.bq_connector.config['project_id']
            inspection_query = f"""
            SELECT 
                lrimoshipno as imo,
                CAST(yearofbuild AS INT64) as yearofbuild,
                COUNT(*) as total_inspections,
                SUM(COALESCE(numberofdefects, 0)) as total_defects,
                SUM(CASE WHEN shipdetained = true THEN 1 ELSE 0 END) as total_detentions
            FROM `{project_id}.public.inspections`
            WHERE lrimoshipno IS NOT NULL
            GROUP BY lrimoshipno, yearofbuild
            """
            inspection_df = self.bq_connector.execute_query(inspection_query)
            
            # Get vessel names (same as original)
            logger.info("Getting vessel names...")
            vessel_names_query = """
            SELECT 
                imo,
                vessel_name
            FROM vessel_detail.vessel_summary
            WHERE vessel_name IS NOT NULL
            """
            vessel_names_df = self.db_connector.execute_query(vessel_names_query)
            
            # Merge all data (same as original)
            logger.info("Merging all vessel data...")
            vessel_data = vessel_policy_df.merge(risk_scores_df, on='imo', how='left')
            vessel_data = vessel_data.merge(inspection_df, on='imo', how='left')
            vessel_data = vessel_data.merge(vessel_names_df, on='imo', how='left')
            
            # Process metrics (same as original)
            vessel_data = self._process_vessel_metrics(vessel_data)

            # Get country data for all vessels
            all_vessel_imos = vessel_data['imo'].dropna().unique()

            # Process risk drivers and features (same as original)
            country_data = self._get_country_data([int(imo) for imo in all_vessel_imos if pd.notna(imo)])
            vessel_data = self._process_risk_drivers_and_features_original(vessel_data, risk_drivers_df, features_df,
                                                                           country_data)
            
            logger.info(f"Collected data for {len(vessel_data)} vessels with {len(vessel_data.columns)} columns")
            
            # Now yield in chunks for memory efficiency
            total_vessels = len(vessel_data)
            for i in range(0, total_vessels, self.chunk_size):
                chunk = vessel_data.iloc[i:i + self.chunk_size].copy()
                chunk_num = i//self.chunk_size + 1
                total_chunks = (total_vessels + self.chunk_size - 1)//self.chunk_size
                logger.info(f"Yielding vessel chunk {chunk_num}/{total_chunks} ({len(chunk)} vessels)")
                yield chunk
                
        except Exception as e:
            logger.error(f"Error collecting vessel data: {e}")
            yield pd.DataFrame()
    
    def _get_vessel_chunk_data(self, vessel_policy_chunk: pd.DataFrame, vessel_imos: List) -> pd.DataFrame:
        """Get complete data for a chunk of vessels with memory optimization."""
        try:
            chunk_data = vessel_policy_chunk.copy()
            imo_list = [int(imo) for imo in vessel_imos if pd.notna(imo)]
            
            if not imo_list:
                return chunk_data
            
            imo_values = ','.join([str(imo) for imo in imo_list])
            
            # Get risk scores
            risk_scores_query = f"""
            SELECT 
                imo,
                ROUND(score, 0) as vessel_risk_score,
                ROUND(detail_score, 0) as expected_loss,
                ROUND(freq_score, 0) as frequency,
                ROUND(sev_score, 0) as severity,
                ROUND(CASE 
                    WHEN vessel_type_detail_score IS NULL OR vessel_type_detail_score < 0 
                    THEN detail_score 
                    ELSE vessel_type_detail_score 
                END, 0) as comparative_vessel_el
            FROM risk_scores.vessel_model_scores_10
            WHERE created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_model_scores_10)
            AND imo IN ({imo_values})
            """
            
            risk_scores_df = self.db_connector.execute_query(risk_scores_query)
            chunk_data = chunk_data.merge(risk_scores_df, on='imo', how='left')
            del risk_scores_df
            
            # Get inspection data from BigQuery - optimized for large datasets
            project_id = self.bq_connector.config['project_id']
            
            # Get inspection data from BigQuery - maintaining full functionality
            inspection_query = f"""
            SELECT 
                CAST(lrimoshipno AS INT64) as imo,
                CAST(yearofbuild AS INT64) as yearofbuild,
                COUNT(*) as total_inspections,
                SUM(COALESCE(numberofdefects, 0)) as total_defects,
                SUM(CASE WHEN shipdetained = true THEN 1 ELSE 0 END) as total_detentions
            FROM `{project_id}.public.inspections`
            WHERE lrimoshipno IN ({imo_values})
            AND lrimoshipno IS NOT NULL
            GROUP BY lrimoshipno, yearofbuild
            """
            
            try:
                inspection_df = self.bq_connector.execute_query(inspection_query)
                chunk_data = chunk_data.merge(inspection_df, on='imo', how='left')
                del inspection_df
            except Exception as e:
                logger.warning(f"Error getting inspection data for chunk: {e}")
                # Add default inspection columns if query fails
                chunk_data['yearofbuild'] = 0
                chunk_data['total_inspections'] = 0
                chunk_data['total_defects'] = 0
                chunk_data['total_detentions'] = 0
            
            # Get vessel names
            vessel_names_query = f"""
            SELECT 
                imo,
                vessel_name
            FROM vessel_detail.vessel_summary
            WHERE vessel_name IS NOT NULL
            AND imo IN ({imo_values})
            """
            
            vessel_names_df = self.db_connector.execute_query(vessel_names_query)
            chunk_data = chunk_data.merge(vessel_names_df, on='imo', how='left')
            del vessel_names_df
            
            # Process metrics and add risk drivers/features
            chunk_data = self._process_vessel_metrics(chunk_data)
            country_data = self._get_country_data(imo_list)
            chunk_data = self._add_risk_drivers_and_features_optimized(chunk_data, imo_list, country_data)
            
            return chunk_data
            
        except Exception as e:
            logger.error(f"Error getting vessel chunk data: {e}")
            return vessel_policy_chunk
    
    def _process_vessel_metrics(self, vessel_data: pd.DataFrame) -> pd.DataFrame:
        """Process vessel metrics with memory optimization."""
        # Fill NaN values in numeric columns
        numeric_columns = ['vessel_risk_score', 'expected_loss', 'frequency', 'severity', 'comparative_vessel_el']
        for col in numeric_columns:
            if col in vessel_data.columns:
                vessel_data[col] = vessel_data[col].fillna(0)
        
        # Calculate derived metrics
        vessel_data['total_expected_loss'] = vessel_data['expected_loss'].fillna(0)
        
        # Handle inspection metrics
        if 'total_inspections' in vessel_data.columns and 'total_defects' in vessel_data.columns:
            vessel_data['total_inspections'] = vessel_data['total_inspections'].fillna(0)
            vessel_data['total_defects'] = vessel_data['total_defects'].fillna(0)
            
            vessel_data['avg_defects_per_inspection'] = (
                vessel_data['total_defects'] / vessel_data['total_inspections'].replace(0, np.nan)
            ).fillna(0).round(0)
        else:
            vessel_data['avg_defects_per_inspection'] = 0
        
        if 'total_inspections' in vessel_data.columns and 'total_detentions' in vessel_data.columns:
            vessel_data['total_detentions'] = vessel_data['total_detentions'].fillna(0)
            
            vessel_data['avg_detentions_per_inspection'] = (
                vessel_data['total_detentions'] / vessel_data['total_inspections'].replace(0, np.nan)
            ).fillna(0).round(0)
        else:
            vessel_data['avg_detentions_per_inspection'] = 0
        
        return vessel_data
    
    def _process_risk_drivers_and_features_original(self, vessel_data: pd.DataFrame, risk_drivers_df: pd.DataFrame, features_df: pd.DataFrame, country_data: pd.DataFrame = None) -> pd.DataFrame:
        """Process risk drivers and features using original logic."""
        try:
            logger.info("Processing risk drivers and features using original approach")

            # Process Risk Drivers (same as original)
            if not risk_drivers_df.empty:
                vessel_risk_drivers = {}
                for imo in vessel_data['imo'].unique():
                    if pd.isna(imo):
                        continue
                    vessel_drivers = risk_drivers_df[risk_drivers_df['imo'] == imo].sort_values('netInfluencePercentage', ascending=False)
                    vessel_risk_drivers[imo] = vessel_drivers.head(10)

                max_risk_drivers = max([len(drivers) for drivers in vessel_risk_drivers.values()]) if vessel_risk_drivers else 0
                max_risk_drivers = min(max_risk_drivers, 10)

                # Initialize Risk Driver columns
                for i in range(1, max_risk_drivers + 1):
                    vessel_data[f'Risk driver {i}'] = '-'
                    vessel_data[f'Risk driver {i} (in %)'] = '-'
                    vessel_data[f'Risk driver sign {i}'] = '-'

                # Populate Risk Driver data
                for imo, drivers in vessel_risk_drivers.items():
                    mask = vessel_data['imo'] == imo
                    for i, (_, driver_row) in enumerate(drivers.iterrows(), 1):
                        if i <= max_risk_drivers:
                            driver_name = driver_row['driverName']
                            net_influence = round(driver_row['netInfluencePercentage'], 2)
                            risk_status = driver_row['riskStatus']

                            if pd.isna(driver_name) or driver_name == '' or driver_name == '-':
                                vessel_data.loc[mask, f'Risk driver {i}'] = '-'
                                vessel_data.loc[mask, f'Risk driver {i} (in %)'] = '-'
                                vessel_data.loc[mask, f'Risk driver sign {i}'] = '-'
                            elif net_influence == 0:
                                vessel_data.loc[mask, f'Risk driver {i}'] = driver_name
                                vessel_data.loc[mask, f'Risk driver {i} (in %)'] = 0
                                vessel_data.loc[mask, f'Risk driver sign {i}'] = 'NEUTRAL'
                            else:
                                vessel_data.loc[mask, f'Risk driver {i}'] = driver_name
                                vessel_data.loc[mask, f'Risk driver {i} (in %)'] = net_influence
                                vessel_data.loc[mask, f'Risk driver sign {i}'] = risk_status

            # Process Features (Influential Factors) (same as original)
            if not features_df.empty:
                vessel_features = {}
                for imo in vessel_data['imo'].unique():
                    if pd.isna(imo):
                        continue
                    vessel_feat = features_df[features_df['imo'] == imo].sort_values('featurePercentage', ascending=False)
                    vessel_features[imo] = vessel_feat

                max_features = max([len(features) for features in vessel_features.values()]) if vessel_features else 0

                # Initialize Influential Factor columns
                for i in range(1, max_features + 1):
                    vessel_data[f'Influential factor {i}'] = '-'
                    vessel_data[f'Influential factor {i} value'] = '-'
                    vessel_data[f'Influential factor {i} percentage'] = '-'
                    vessel_data[f'Influential factor {i} (Risk-driver)'] = '-'
                    vessel_data[f'Influential factor {i} sign'] = '-'

                # Populate Influential Factor data
                for imo, features in vessel_features.items():
                    mask = vessel_data['imo'] == imo
                    for i, (_, feature_row) in enumerate(features.iterrows(), 1):
                        if i <= max_features:
                            field_name = feature_row['fieldName'] if pd.notna(feature_row['fieldName']) else feature_row['key']
                            feature_value = feature_row['featureValue']
                            feature_unit = feature_row['featureValueUnit'] if pd.notna(feature_row['featureValueUnit']) else ''
                            feature_percentage = feature_row['featurePercentage'] if pd.notna(feature_row['featurePercentage']) else None
                            risk_driver = feature_row['riskDriver']
                            risk_status = feature_row['riskStatus']

                            if pd.isna(field_name) or field_name == '' or field_name == '-':
                                vessel_data.loc[mask, f'Influential factor {i}'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} value'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} percentage'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} (Risk-driver)'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} sign'] = '-'
                            else:
                                # Check if this is a country feature and handle accordingly
                                if feature_row['key'] in ['registeredownercountryofdomicile',
                                                         'registeredownercountryofregistration',
                                                         'groupbeneficialownercountryofdomicile',
                                                         'groupbeneficialownercountryofregistration']:
                                    formatted_value = self._get_vessel_country_value(imo, feature_row['key'], country_data)
                                else:
                                    formatted_value = self._format_feature_value_with_unit(feature_value, feature_unit)
                                formatted_percentage = round(float(feature_percentage), 2) if feature_percentage is not None else '-'

                                vessel_data.loc[mask, f'Influential factor {i}'] = field_name
                                vessel_data.loc[mask, f'Influential factor {i} value'] = formatted_value
                                vessel_data.loc[mask, f'Influential factor {i} percentage'] = formatted_percentage
                                vessel_data.loc[mask, f'Influential factor {i} (Risk-driver)'] = risk_driver if pd.notna(risk_driver) else '-'
                                vessel_data.loc[mask, f'Influential factor {i} sign'] = risk_status if pd.notna(risk_status) else '-'

            logger.info("Risk drivers and features processing completed successfully")
            return vessel_data

        except Exception as e:
            logger.error(f"Error processing risk drivers and features: {e}")
            return vessel_data

    def _add_risk_drivers_and_features_optimized(self, chunk_data: pd.DataFrame, imo_list: List[int], country_data: pd.DataFrame = None) -> pd.DataFrame:
        """Add risk drivers and features maintaining original functionality with memory optimization."""
        try:
            if not imo_list:
                return self._add_empty_risk_columns(chunk_data)

            imo_values = ','.join([str(imo) for imo in imo_list])

            # Maintain original functionality - calculate max features dynamically
            max_drivers = 10  # Same as original
            max_features = 100  # Use high limit for query, then calculate actual max

            # Get risk drivers
            risk_drivers_query = f"""
            WITH ranked_drivers AS (
                SELECT 
                    vf.imo,
                    vf.riskdriver AS "driverName",
                    round(sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END)),4) AS "positiveInfluencePercentage",
                    round(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)),4) AS "negativeInfluencePercentage",
                    round(abs(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) - sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))),4) AS "netInfluencePercentage",
                    CASE 
                        WHEN sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) > sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))
                        THEN 'POSITIVE'
                        ELSE 'NEGATIVE'
                    END AS "riskStatus",
                    ROW_NUMBER() OVER (PARTITION BY vf.imo ORDER BY abs(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) - sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))) DESC) as rn
                FROM risk_scores.vessel_feature_values_10 vf
                WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
                AND vf.imo IN ({imo_values})
                GROUP BY vf.imo, vf.riskdriver
            )
            SELECT * FROM ranked_drivers WHERE rn <= {max_drivers}
            ORDER BY imo, "netInfluencePercentage" DESC
            """

            # Get features (influential factors)
            features_query = f"""
            SELECT 
                ranked_vf.imo,
                ranked_vf.feature as "key",
                fd.field_name as "fieldName",
                fd.description as "description",
                fd.unit as "featureValueUnit",
                abs(round(ranked_vf.shap_impact_percentage,4))::real as "riskValue",
                ranked_vf.riskdriver as "riskDriver",
                abs(round(ranked_vf.shap_impact_percentage,4))::real as "featurePercentage",
                ranked_vf.shap_impact_sign as "riskStatus",
                abs(round(ranked_vf.feature_value,4))::real as "featureValue"
            FROM (
                SELECT vf.imo, vf.feature, vf.shap_impact_percentage, vf.riskdriver, vf.shap_impact_sign, vf.feature_value, vf.created_on,
                       ROW_NUMBER() OVER (PARTITION BY vf.imo ORDER BY abs(vf.shap_impact_percentage) DESC) as rn
                FROM risk_scores.vessel_feature_values_10 vf
                WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
                AND vf.imo IN ({imo_values})
            ) ranked_vf
            LEFT JOIN risk_scores.feature_dictionary_10 fd ON ranked_vf.feature = fd.name AND ranked_vf.created_on = fd.created_on
            WHERE ranked_vf.rn <= {max_features}
            ORDER BY ranked_vf.imo, ranked_vf.shap_impact_percentage DESC
            """

            # Execute queries
            risk_drivers_df = self.db_connector.execute_query(risk_drivers_query)
            features_df = self.db_connector.execute_query(features_query)

            # Calculate actual max features based on data like original
            vessel_feature_counts = []
            for imo in chunk_data['imo'].unique():
                if pd.isna(imo):
                    continue
                imo_features = features_df[features_df['imo'] == imo] if not features_df.empty else pd.DataFrame()
                vessel_feature_counts.append(len(imo_features))

            actual_max_features = max(vessel_feature_counts) if vessel_feature_counts else 0

            # Initialize all columns (same as original)
            for i in range(1, max_drivers + 1):
                chunk_data[f'Risk driver {i}'] = '-'
                chunk_data[f'Risk driver {i} (in %)'] = '-'
                chunk_data[f'Risk driver sign {i}'] = '-'

            for i in range(1, actual_max_features + 1):
                chunk_data[f'Influential factor {i}'] = '-'
                chunk_data[f'Influential factor {i} value'] = '-'
                chunk_data[f'Influential factor {i} percentage'] = '-'
                chunk_data[f'Influential factor {i} (Risk-driver)'] = '-'
                chunk_data[f'Influential factor {i} sign'] = '-'

            # Populate risk driver data
            if not risk_drivers_df.empty:
                for imo in chunk_data['imo'].unique():
                    if pd.isna(imo):
                        continue

                    vessel_drivers = risk_drivers_df[risk_drivers_df['imo'] == imo].head(max_drivers)
                    mask = chunk_data['imo'] == imo

                    for i, (_, driver_row) in enumerate(vessel_drivers.iterrows(), 1):
                        if i <= max_drivers:
                            chunk_data.loc[mask, f'Risk driver {i}'] = driver_row['driverName'] if pd.notna(driver_row['driverName']) else '-'
                            chunk_data.loc[mask, f'Risk driver {i} (in %)'] = round(driver_row['netInfluencePercentage'], 2) if pd.notna(driver_row['netInfluencePercentage']) else '-'
                            chunk_data.loc[mask, f'Risk driver sign {i}'] = driver_row['riskStatus'] if pd.notna(driver_row['riskStatus']) else '-'

            # Populate features data (same logic as original)
            if not features_df.empty:
                for imo in chunk_data['imo'].unique():
                    if pd.isna(imo):
                        continue

                    vessel_features = features_df[features_df['imo'] == imo].head(actual_max_features)
                    mask = chunk_data['imo'] == imo

                    for i, (_, feature_row) in enumerate(vessel_features.iterrows(), 1):
                        if i <= actual_max_features:
                            field_name = feature_row['fieldName'] if pd.notna(feature_row['fieldName']) else feature_row['key']
                            feature_value = feature_row['featureValue']
                            feature_unit = feature_row['featureValueUnit'] if pd.notna(feature_row['featureValueUnit']) else ''
                            feature_percentage = feature_row['featurePercentage']
                            risk_driver = feature_row['riskDriver']
                            risk_status = feature_row['riskStatus']

                            if pd.notna(field_name) and field_name != '' and field_name != '-':
                                # Check if this is a country feature and handle accordingly
                                if feature_row['key'] in ['registeredownercountryofdomicile',
                                                         'registeredownercountryofregistration',
                                                         'groupbeneficialownercountryofdomicile',
                                                         'groupbeneficialownercountryofregistration']:
                                    formatted_value = self._get_vessel_country_value(imo, feature_row['key'], country_data)
                                else:
                                    formatted_value = self._format_feature_value_with_unit(feature_value, feature_unit)
                                formatted_percentage = round(float(feature_percentage), 2) if pd.notna(feature_percentage) else '-'

                                chunk_data.loc[mask, f'Influential factor {i}'] = field_name
                                chunk_data.loc[mask, f'Influential factor {i} value'] = formatted_value
                                chunk_data.loc[mask, f'Influential factor {i} percentage'] = formatted_percentage
                                chunk_data.loc[mask, f'Influential factor {i} (Risk-driver)'] = risk_driver if pd.notna(risk_driver) else '-'
                                chunk_data.loc[mask, f'Influential factor {i} sign'] = risk_status if pd.notna(risk_status) else '-'

            del risk_drivers_df, features_df
            return chunk_data

        except Exception as e:
            logger.error(f"Error adding risk drivers and features: {e}")
            return self._add_empty_risk_columns(chunk_data)

    def _add_empty_risk_columns(self, chunk_data: pd.DataFrame) -> pd.DataFrame:
        """Add empty risk driver and feature columns for consistency."""
        # Add 10 risk drivers (same as original)
        for i in range(1, 11):
            chunk_data[f'Risk driver {i}'] = '-'
            chunk_data[f'Risk driver {i} (in %)'] = '-'
            chunk_data[f'Risk driver sign {i}'] = '-'

        # Add 10 influential factors (same as original)
        for i in range(1, 11):
            chunk_data[f'Influential factor {i}'] = '-'
            chunk_data[f'Influential factor {i} value'] = '-'
            chunk_data[f'Influential factor {i} percentage'] = '-'
            chunk_data[f'Influential factor {i} (Risk-driver)'] = '-'
            chunk_data[f'Influential factor {i} sign'] = '-'

        return chunk_data

    def _format_feature_value_with_unit(self, feature_value, feature_unit):
        """Format feature value with unit concatenation (same as original)."""
        if pd.isna(feature_value) or feature_value == '' or feature_value == '-':
            return '-'

        try:
            numeric_value = float(feature_value)
            rounded_value = round(numeric_value, 2)
        except (ValueError, TypeError):
            return str(feature_value)

        if pd.isna(feature_unit) or feature_unit == '':
            return str(rounded_value)

        # Units that should NOT be concatenated (same as original)
        no_concat_units = {'binary', 'char', 'number', 'index', 'value'}
        unit_str = str(feature_unit).lower().strip()

        if unit_str in no_concat_units:
            return str(rounded_value)
        else:
            return f"{rounded_value} {feature_unit}"

    def _format_feature_value_with_country_override(self, feature_key: str, feature_value: float, feature_unit: str, imo: int, country_data: pd.DataFrame) -> str:
        """Format feature value with country data override for specific features."""
        country_features = {
            'registeredownercountryofdomicile': 'registeredownercountryofdomicile_value',
            'registeredownercountryofregistration': 'registeredownercountryofregistration_value',
            'groupbeneficialownercountryofdomicile': 'groupbeneficialownercountryofdomicile_value',
            'groupbeneficialownercountryofregistration': 'groupbeneficialownercountryofregistration_value'
        }

        if feature_key in country_features and not country_data.empty:
            country_row = country_data[country_data['imo'] == imo]
            if not country_row.empty:
                country_value = country_row[country_features[feature_key]].iloc[0]
                if pd.notna(country_value) and country_value != '':
                    return str(country_value)
            else:
                return '-'  # Return hyphen if no country data found

        # Fallback to original formatting for other features
        return self._format_feature_value_with_unit(feature_value, feature_unit)

    def _get_most_common_country_for_account(self, vessel_imos: List[int], feature_key: str, country_data: pd.DataFrame) -> str:
        """Get most common country value for account-level aggregation."""
        country_mapping = {
            'registeredownercountryofdomicile': 'registeredownercountryofdomicile_value',
            'registeredownercountryofregistration': 'registeredownercountryofregistration_value',
            'groupbeneficialownercountryofdomicile': 'groupbeneficialownercountryofdomicile_value',
            'groupbeneficialownercountryofregistration': 'groupbeneficialownercountryofregistration_value'
        }

        if feature_key not in country_mapping or country_data.empty:
            return '-'

        account_countries = country_data[country_data['imo'].isin(vessel_imos)][country_mapping[feature_key]]
        account_countries = account_countries.dropna()

        if account_countries.empty:
            return '-'

        # Return most common country
        return account_countries.mode().iloc[0] if not account_countries.mode().empty else '-'

    def _get_vessel_country_value(self, imo: int, feature_key: str, country_data: pd.DataFrame) -> str:
        """Get country value for individual vessel."""
        country_mapping = {
            'registeredownercountryofdomicile': 'registeredownercountryofdomicile_value',
            'registeredownercountryofregistration': 'registeredownercountryofregistration_value',
            'groupbeneficialownercountryofdomicile': 'groupbeneficialownercountryofdomicile_value',
            'groupbeneficialownercountryofregistration': 'groupbeneficialownercountryofregistration_value'
        }

        if feature_key not in country_mapping or country_data.empty:
            return '-'

        vessel_country_row = country_data[country_data['imo'] == imo]
        if vessel_country_row.empty:
            return '-'

        country_value = vessel_country_row[country_mapping[feature_key]].iloc[0]
        return str(country_value) if pd.notna(country_value) and country_value != '' else '-'

    def _aggregate_account_data_original(self, vessel_data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate vessel data to account level using original approach with full functionality."""
        logger.info("Aggregating data to account level using original approach")

        if vessel_data.empty:
            return pd.DataFrame()

        try:
            account_groups = vessel_data.groupby(['policy_id', 'account_name'])

            # Get account-level risk drivers and features (same as original)
            all_vessel_imos = vessel_data['imo'].dropna().unique()
            country_data = self._get_country_data([int(imo) for imo in all_vessel_imos if pd.notna(imo)])

            if len(all_vessel_imos) > 0:
                all_account_risk_drivers_data, all_account_features_data = self._fetch_account_risk_data(
                    all_vessel_imos)
            else:
                all_account_risk_drivers_data = pd.DataFrame()
                all_account_features_data = pd.DataFrame()

            # Determine max columns needed (same as original)
            all_account_risk_drivers = []
            all_account_features = []
            for (policy_id, account_name), group in account_groups:
                vessel_imos = group['imo'].dropna().unique()
                policy_risk_drivers = all_account_risk_drivers_data[all_account_risk_drivers_data['imo'].isin(vessel_imos)] if not all_account_risk_drivers_data.empty else pd.DataFrame()
                policy_features = all_account_features_data[all_account_features_data['imo'].isin(vessel_imos)] if not all_account_features_data.empty else pd.DataFrame()

                risk_driver_count = len(policy_risk_drivers.groupby('driverName').first()) if not policy_risk_drivers.empty else 0
                feature_count = len(policy_features.groupby(['key', 'riskDriver']).first()) if not policy_features.empty else 0

                all_account_risk_drivers.append(min(risk_driver_count, 100))
                all_account_features.append(min(feature_count, 100))

            max_risk_drivers = max(all_account_risk_drivers) if all_account_risk_drivers else 0
            max_features = max(all_account_features) if all_account_features else 0

            account_data = []

            for (policy_id, account_name), group in account_groups:
                # Get policy_number from the first row of the group
                policy_number = group['policy_number'].iloc[0] if 'policy_number' in group.columns and not group['policy_number'].empty else policy_id
                record = {
                    'Account Name': str(account_name) if account_name is not None else '-',
                    'UMR': str(policy_number) if policy_number is not None else '-',
                    'Account Risk Score': round(float(group['vessel_risk_score'].mean()), 0) if not group['vessel_risk_score'].isna().all() else 0,
                    'Frequency': round(float(group['frequency'].mean()), 0) if not group['frequency'].isna().all() else 0,
                    'Severity': round(float(group['severity'].mean()), 0) if not group['severity'].isna().all() else 0,
                    'Total expected loss (in USD)': round(float(group['expected_loss'].sum()), 0) if not group['expected_loss'].isna().all() else 0,
                    'Comparative Account EL (in USD)': round(float(group['comparative_vessel_el'].sum()), 0) if 'comparative_vessel_el' in group.columns and not group['comparative_vessel_el'].isna().all() else 0,
                    'No. of Vessels': int(len(group))
                }

                # Calculate average year of build
                if 'yearofbuild' in group.columns:
                    valid_years = group['yearofbuild'].dropna()
                    record['Avg. year of build'] = int(round(float(valid_years.mean()))) if len(valid_years) > 0 else '-'
                else:
                    record['Avg. year of build'] = '-'

                # Calculate inspection metrics
                total_defects = group['total_defects'].fillna(0).sum()
                total_inspections = group['total_inspections'].fillna(0).sum()
                record['Average no. of defects per inspection'] = round(float(total_defects / total_inspections), 0) if total_inspections > 0 else 0

                total_detentions = group['total_detentions'].fillna(0).sum()
                record['Average no. of detentions per inspection'] = round(float(total_detentions / total_inspections), 0) if total_inspections > 0 else 0

                # Process account-level risk drivers and features (same as original)
                vessel_imos = group['imo'].dropna().unique()
                account_risk_drivers = self._get_account_risk_drivers(all_account_risk_drivers_data, vessel_imos)
                account_features = self._get_account_features(all_account_features_data, vessel_imos)

                # Add risk driver columns (same as original)
                for i in range(1, max_risk_drivers + 1):
                    if i <= len(account_risk_drivers):
                        driver = account_risk_drivers[i-1]
                        record[f'Risk driver {i}'] = driver['driverName']
                        record[f'Risk driver {i} (in %)'] = round(driver['netInfluencePercentage'], 2)
                        record[f'Risk driver sign {i}'] = driver['riskStatus']
                    else:
                        record[f'Risk driver {i}'] = '-'
                        record[f'Risk driver {i} (in %)'] = '-'
                        record[f'Risk driver sign {i}'] = '-'

                # Initialize all feature columns with hyphens first (same as original)
                for i in range(1, max_features + 1):
                    record[f'Influential factor {i}'] = '-'
                    record[f'Influential factor {i} value'] = '-'
                    record[f'Influential factor {i} percentage'] = '-'
                    record[f'Influential factor {i} (Risk-driver)'] = '-'
                    record[f'Influential factor {i} sign'] = '-'

                # Add feature columns (same as original)
                for i in range(1, max_features + 1):
                    if i <= len(account_features):
                        feature = account_features[i-1]
                        field_name = feature['fieldName'] if pd.notna(feature['fieldName']) else feature['key']
                        feature_value = feature['featureValue']
                        feature_unit = feature['featureValueUnit']
                        feature_percentage = feature['featurePercentage']
                        risk_driver = feature['riskDriver']
                        risk_status = feature['riskStatus']

                        if field_name == '-' or field_name == '' or pd.isna(field_name):
                            continue  # Keep hyphens
                        else:
                            if feature['key'] in ['registeredownercountryofdomicile',
                                                  'registeredownercountryofregistration',
                                                  'groupbeneficialownercountryofdomicile',
                                                  'groupbeneficialownercountryofregistration']:
                                formatted_value = self._get_most_common_country_for_account(vessel_imos, feature['key'],
                                                                                            country_data)
                            else:
                                formatted_value = self._format_feature_value_with_unit(feature_value, feature_unit)
                            formatted_percentage = round(float(feature_percentage), 2) if feature_percentage is not None and pd.notna(feature_percentage) else '-'

                            record[f'Influential factor {i}'] = field_name
                            record[f'Influential factor {i} value'] = formatted_value
                            record[f'Influential factor {i} percentage'] = formatted_percentage
                            record[f'Influential factor {i} (Risk-driver)'] = risk_driver if pd.notna(risk_driver) else '-'
                            record[f'Influential factor {i} sign'] = risk_status if pd.notna(risk_status) else '-'

                account_data.append(record)

            return pd.DataFrame(account_data)

        except Exception as e:
            logger.error(f"Error aggregating account data: {e}")
            return pd.DataFrame()

    def _aggregate_account_data_optimized(self, vessel_data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate vessel data to account level with memory optimization."""
        logger.info("Aggregating data to account level")

        if vessel_data.empty:
            return pd.DataFrame()

        try:
            account_groups = vessel_data.groupby(['policy_id', 'account_name'])
            account_data = []

            for (policy_id, account_name), group in account_groups:
                # Get policy_number from the first row of the group
                policy_number = group['policy_number'].iloc[0] if 'policy_number' in group.columns and not group['policy_number'].empty else policy_id
                record = {
                    'Account Name': str(account_name) if account_name is not None else '-',
                    'Policy Number': str(policy_number) if policy_number is not None else '-',
                    'Account Risk Score': round(float(group['vessel_risk_score'].mean()), 0) if not group['vessel_risk_score'].isna().all() else 0,
                    'Frequency': round(float(group['frequency'].mean()), 0) if not group['frequency'].isna().all() else 0,
                    'Severity': round(float(group['severity'].mean()), 0) if not group['severity'].isna().all() else 0,
                    'Total expected loss (in USD)': round(float(group['total_expected_loss'].sum()), 0) if not group['total_expected_loss'].isna().all() else 0,
                    'Comparative Account EL (in USD)': round(float(group['comparative_vessel_el'].sum()), 0) if 'comparative_vessel_el' in group.columns and not group['comparative_vessel_el'].isna().all() else 0,
                    'No. of Vessels': int(len(group))
                }

                # Calculate average year of build
                if 'yearofbuild' in group.columns:
                    valid_years = group['yearofbuild'].dropna()
                    record['Avg. year of build'] = int(round(float(valid_years.mean()))) if len(valid_years) > 0 else '-'
                else:
                    record['Avg. year of build'] = '-'

                # Calculate inspection metrics
                total_defects = group['total_defects'].fillna(0).sum()
                total_inspections = group['total_inspections'].fillna(0).sum()
                record['Average no. of defects per inspection'] = round(float(total_defects / total_inspections), 0) if total_inspections > 0 else 0

                total_detentions = group['total_detentions'].fillna(0).sum()
                record['Average no. of detentions per inspection'] = round(float(total_detentions / total_inspections), 0) if total_inspections > 0 else 0

                account_data.append(record)

            return pd.DataFrame(account_data)

        except Exception as e:
            logger.error(f"Error aggregating account data: {e}")
            return pd.DataFrame()

    def _format_cell_value(self, value) -> Union[str, int, float]:
        """Format cell value for Excel output."""
        if pd.isna(value) or value == '' or value == 'nan':
            return '-'
        elif isinstance(value, (int, float)):
            if np.isnan(value):
                return '-'
            return value
        else:
            return str(value)

    def _auto_adjust_columns(self, worksheet, max_columns: int):
        """Auto-adjust column widths."""
        try:
            for i in range(max_columns):
                worksheet.set_column(i, i, 15)  # Set reasonable default width
        except Exception as e:
            logger.error(f"Error adjusting column widths: {e}")

    def _upload_buffer_to_gcs(self, excel_buffer: io.BytesIO, blob_name: str) -> str:
        """Upload Excel buffer to GCS with detailed logging."""
        try:
            if not self.storage_client or not self.bucket_name:
                raise ValueError("GCS storage client and bucket name must be provided")

            # Get buffer size for logging
            excel_buffer.seek(0, 2)  # Seek to end
            buffer_size_mb = excel_buffer.tell() / (1024 * 1024)
            excel_buffer.seek(0)  # Reset to beginning

            logger.info(f"Starting GCS upload: {blob_name} ({buffer_size_mb:.1f} MB)")

            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(blob_name)

            # Set content type
            blob.content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            # Upload from buffer
            upload_start = datetime.now()
            blob.upload_from_file(excel_buffer)
            upload_duration = (datetime.now() - upload_start).total_seconds()

            gcs_path = f"gs://{self.bucket_name}/{blob_name}"
            upload_speed_mbps = buffer_size_mb / upload_duration if upload_duration > 0 else 0

            logger.info(f"GCS upload completed: {gcs_path}")
            logger.info(f"Upload stats: {buffer_size_mb:.1f} MB in {upload_duration:.1f}s ({upload_speed_mbps:.1f} MB/s)")

            return gcs_path

        except Exception as e:
            logger.error(f"Error uploading to GCS: {e}")
            raise

    def get_memory_usage_info(self) -> Dict:
        """Get memory usage configuration info."""
        return {
            'chunk_size': self.chunk_size,
            'max_memory_mb': self.max_memory_mb,
            'constant_memory_mode': True,
            'streaming_enabled': True,
            'temp_directory': '/tmp'
        }

    def _get_account_data_chunks(self, tenant_id: str) -> Iterator[pd.DataFrame]:
        """Get account data using original aggregation approach."""
        try:
            logger.info(f"Getting account data for tenant: {tenant_id} using original approach")

            # Get all vessel data first (same as original)
            all_vessel_chunks = list(self._get_vessel_data_chunks(tenant_id))

            if not all_vessel_chunks:
                yield pd.DataFrame()
                return

            # Combine vessel data for aggregation (same as original)
            logger.info("Combining vessel data for account aggregation")
            vessel_data = pd.concat(all_vessel_chunks, ignore_index=True)

            # Clear individual chunks from memory
            del all_vessel_chunks

            # Aggregate to account level using original method
            account_data = self._aggregate_account_data_original(vessel_data)

            # Clear vessel data from memory
            del vessel_data

            if account_data.empty:
                logger.warning(f"No account data generated for tenant: {tenant_id}")
                yield pd.DataFrame()
                return

            logger.info(f"Account aggregation completed: {len(account_data)} accounts with {len(account_data.columns)} columns")

            # Yield account data in chunks
            for i in range(0, len(account_data), self.chunk_size):
                chunk = account_data.iloc[i:i + self.chunk_size].copy()
                yield chunk

        except Exception as e:
            logger.error(f"Error getting account data: {e}")
            yield pd.DataFrame()

    def _fetch_account_risk_data(self, all_vessel_imos) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Fetch account-level risk drivers and features data (same as original)."""
        try:
            imo_list = [int(imo) for imo in all_vessel_imos if pd.notna(imo)]
            if not imo_list:
                return pd.DataFrame(), pd.DataFrame()

            imo_values = ','.join([str(imo) for imo in imo_list])

            # Get risk drivers data (same as original)
            risk_drivers_query = f"""
            SELECT 
                vf.imo,
                vf.riskdriver AS "driverName",
                sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END)) AS "positiveInfluencePercentage",
                sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) AS "negativeInfluencePercentage",
                abs(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) - sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))) AS "netInfluencePercentage",
                CASE 
                    WHEN sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) > sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END)) 
                    THEN 'POSITIVE' 
                    ELSE 'NEGATIVE' 
                END AS "riskStatus"
            FROM risk_scores.vessel_feature_values_10 vf
            WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
            AND vf.imo IN ({imo_values})
            GROUP BY vf.imo, vf.riskdriver
            """
            all_risk_drivers_df = self.db_connector.execute_query(risk_drivers_query)

            # Get features data (same as original)
            features_query = f"""
            SELECT 
                ranked_vf.imo,
                ranked_vf.feature as "key",
                fd.field_name as "fieldName",
                fd.description as "description",
                fd.unit as "featureValueUnit",
                abs(round(ranked_vf.shap_impact_percentage,4)) as "riskValue",
                ranked_vf.riskdriver as "riskDriver",
                abs(round(ranked_vf.shap_impact_percentage,4)) as "featurePercentage",
                ranked_vf.shap_impact_sign as "riskStatus",
                abs(round(ranked_vf.feature_value,4)) as "featureValue"
            FROM (
                SELECT vf.imo, vf.feature, vf.shap_impact_percentage, vf.riskdriver, vf.shap_impact_sign, vf.feature_value, vf.created_on,
                       ROW_NUMBER() OVER (PARTITION BY vf.imo ORDER BY abs(vf.shap_impact_percentage) DESC) as rn
                FROM risk_scores.vessel_feature_values_10 vf
                WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
                AND vf.imo IN ({imo_values})
            ) ranked_vf
            LEFT JOIN risk_scores.feature_dictionary_10 fd ON ranked_vf.feature = fd.name AND ranked_vf.created_on = fd.created_on
            WHERE ranked_vf.rn <= 100
            """
            all_features_df = self.db_connector.execute_query(features_query)

            return all_risk_drivers_df, all_features_df

        except Exception as e:
            logger.error(f"Error fetching account risk data: {e}")
            return pd.DataFrame(), pd.DataFrame()

    def _get_account_risk_drivers(self, all_risk_drivers_df: pd.DataFrame, vessel_imos) -> List[Dict]:
        """Get aggregated risk drivers for account (same as original)."""
        if all_risk_drivers_df.empty:
            return []

        policy_risk_drivers = all_risk_drivers_df[all_risk_drivers_df['imo'].isin(vessel_imos)]
        if policy_risk_drivers.empty:
            return []

        # Aggregate by risk driver
        driver_agg = policy_risk_drivers.groupby('driverName').agg({
            'positiveInfluencePercentage': 'mean',
            'negativeInfluencePercentage': 'mean',
            'netInfluencePercentage': 'mean'
        }).reset_index()

        # Determine overall risk status
        driver_agg['riskStatus'] = driver_agg.apply(
            lambda row: 'POSITIVE' if row['negativeInfluencePercentage'] > row['positiveInfluencePercentage'] else 'NEGATIVE',
            axis=1
        )

        # Sort by net influence and return top 10
        driver_agg = driver_agg.sort_values('netInfluencePercentage', ascending=False).head(10)
        return driver_agg.to_dict('records')

    def _get_account_features(self, all_features_df: pd.DataFrame, vessel_imos) -> List[Dict]:
        """Get aggregated features for account (same as original)."""
        if all_features_df.empty:
            return []

        policy_features = all_features_df[all_features_df['imo'].isin(vessel_imos)]
        if policy_features.empty:
            return []

        # Aggregate by feature and risk driver
        feature_agg = policy_features.groupby(['key', 'fieldName', 'description', 'featureValueUnit', 'riskDriver']).agg({
            'riskValue': 'mean',
            'featurePercentage': 'mean',
            'featureValue': 'mean'
        }).reset_index()

        # Determine overall risk status
        feature_agg['riskStatus'] = 'POSITIVE'  # Simplified for account level

        # Sort by feature percentage and return top 10
        feature_agg = feature_agg.sort_values('featurePercentage', ascending=False)
        return feature_agg.to_dict('records')

    def _get_country_data(self, imo_list: List[int]) -> pd.DataFrame:
        """Fetch country data from BigQuery ship_data table."""
        try:
            if not imo_list:
                return pd.DataFrame()

            project_id = self.bq_connector.config['project_id']
            imo_values = ','.join([str(imo) for imo in imo_list])

            country_query = f"""
            SELECT 
                CAST(LRIMOShipNo AS INT64) as imo,
                COALESCE(RegisteredOwnerCountryofDomicile, '-') as registeredownercountryofdomicile_value,
                COALESCE(RegisteredOwnerCountryOfRegistration, '-') as registeredownercountryofregistration_value,
                COALESCE(GroupBeneficialOwnerCountryofDomicile, '-') as groupbeneficialownercountryofdomicile_value,
                COALESCE(GroupBeneficialOwnerCountryOfRegistration, '-') as groupbeneficialownercountryofregistration_value
            FROM `{project_id}.public.ship_data`
            WHERE LRIMOShipNo IN ({imo_values})
            AND LRIMOShipNo IS NOT NULL
            """

            country_data = self.bq_connector.execute_query(country_query)

            # Fill any remaining NaN values with hyphens
            country_columns = ['registeredownercountryofdomicile_value', 'registeredownercountryofregistration_value',
                              'groupbeneficialownercountryofdomicile_value', 'groupbeneficialownercountryofregistration_value']
            for col in country_columns:
                if col in country_data.columns:
                    country_data[col] = country_data[col].fillna('-')
                    country_data[col] = country_data[col].replace('', '-')

            return country_data

        except Exception as e:
            logger.error(f"Error fetching country data: {e}")
            return pd.DataFrame()
